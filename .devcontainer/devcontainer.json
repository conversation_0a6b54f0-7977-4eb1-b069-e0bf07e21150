// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "Python 3 Project",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	//"image": "mcr.microsoft.com/devcontainers/python:3",
	"build": {
		"dockerfile": "Dockerfile"
	},
	"containerEnv": {
		"PYTHONPATH": "./src"
	},
	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {
		"ghcr.io/devcontainers/features/node:1": {}
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [
		3000
	],
	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": ".devcontainer/post-create.sh",
	"postStartCommand": ".devcontainer/post-start.sh",
	// Configure tool-specific properties.
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.mypy-type-checker",
				"charliermarsh.ruff"
			]
		}
	}
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
