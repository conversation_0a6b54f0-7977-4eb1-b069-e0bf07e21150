# Smithery.ai configuration for mcp-atlassian
startCommand:
  type: stdio # Specifies the server communicates over standard input/output
  configSchema:
    # JSO<PERSON> Schema defining the configuration options users need to provide
    type: object
    required:
      - jiraUrl
      - confluenceUrl
      # Add other strictly required credentials based on auth needs
    properties:
      # Confluence Config
      confluenceUrl:
        type: string
        description: "Base URL for your Confluence instance (e.g., https://your-domain.atlassian.net/wiki or https://confluence.yourcompany.com)."
      confluenceUsername:
        type: string
        description: "(Optional for Cloud Basic Auth) Your Confluence username or email."
      confluenceApiToken:
        type: string
        description: "(Optional for Cloud Basic Auth) Your Confluence API token."
        format: password # Hides the value in UI inputs
      confluencePersonalToken:
        type: string
        description: "(Optional for Server/DC Token Auth) Your Confluence Personal Access Token."
        format: password
      confluenceSslVerify:
        type: boolean
        description: "(Optional, Server/DC only) Verify SSL certificate for Confluence. Defaults to true."
        default: true
      confluenceSpacesFilter:
        type: string
        description: "(Optional) Comma-separated list of Confluence space keys to limit searches to (e.g., 'DEV,QA')."

      # Jira Config
      jiraUrl:
        type: string
        description: "Base URL for your Jira instance (e.g., https://your-domain.atlassian.net or https://jira.yourcompany.com)."
      jiraUsername:
        type: string
        description: "(Optional for Cloud Basic Auth or Server/DC Basic Auth) Your Jira username or email."
      jiraApiToken:
        type: string
        description: "(Optional for Cloud Basic Auth or Server/DC Basic Auth) Your Jira API token."
        format: password
      jiraPersonalToken:
        type: string
        description: "(Optional for Server/DC Token Auth) Your Jira Personal Access Token."
        format: password
      jiraSslVerify:
        type: boolean
        description: "(Optional, Server/DC only) Verify SSL certificate for Jira. Defaults to true."
        default: true
      jiraProjectsFilter:
        type: string
        description: "(Optional) Comma-separated list of Jira project keys to limit searches to (e.g., 'PROJ1,PROJ2')."

      # General Config
      readOnlyMode:
        type: boolean
        description: "(Optional) Run in read-only mode (prevents create/update/delete). Defaults to false."
        default: false

    additionalProperties: false # Disallow properties not defined above

  commandFunction:
    # A JavaScript function that produces the CLI command and environment variables
    # needed to start the MCP server, based on the user's configuration.
    |-
    (config) => {
      // The command matches the ENTRYPOINT in the Dockerfile
      const command = 'mcp-atlassian';
      const args = []; // No arguments needed as config is via ENV

      // Map the config schema properties to the environment variables
      const env = {
        // Confluence ENV VARS
        CONFLUENCE_URL: config.confluenceUrl,
        CONFLUENCE_USERNAME: config.confluenceUsername,
        CONFLUENCE_API_TOKEN: config.confluenceApiToken,
        CONFLUENCE_PERSONAL_TOKEN: config.confluencePersonalToken,
        CONFLUENCE_SSL_VERIFY: config.confluenceSslVerify !== undefined ? String(config.confluenceSslVerify) : 'true',
        CONFLUENCE_SPACES_FILTER: config.confluenceSpacesFilter,

        // Jira ENV VARS
        JIRA_URL: config.jiraUrl,
        JIRA_USERNAME: config.jiraUsername,
        JIRA_API_TOKEN: config.jiraApiToken,
        JIRA_PERSONAL_TOKEN: config.jiraPersonalToken,
        JIRA_SSL_VERIFY: config.jiraSslVerify !== undefined ? String(config.jiraSslVerify) : 'true',
        JIRA_PROJECTS_FILTER: config.jiraProjectsFilter,

        // General ENV VARS
        READ_ONLY_MODE: config.readOnlyMode !== undefined ? String(config.readOnlyMode) : 'false',
      };

      // Filter out undefined/null env variables
      const filteredEnv = Object.entries(env)
        .filter(([key, value]) => value !== undefined && value !== null)
        .reduce((obj, [key, value]) => {
          obj[key] = value;
          return obj;
        }, {});

      return {
        command: command,
        args: args,
        env: filteredEnv
      };
    }
