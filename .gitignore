# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environments
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# uv/uvx specific
.uv/
.uvx/
.venv.uv/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific
.DS_Store
Thumbs.db

# Debug and local development
debug/
local/

# Credentials
.pypirc

# Pytest
.pytest_cache/
.coverage/
.coverage

# Pre-commit
.pre-commit-config.yaml.cache

# debug
playground/

# cursor
.cursor*

# Claude
.claude/
